{"name": "mcp-server-nocobase", "version": "0.1.0", "description": "MCP Server for NocoBase - Provides tools and resources for interacting with NocoBase applications", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "jest", "test:mcp": "node tests/test-mcp-tools.js", "test:relations": "node tests/test-relation-fields-complete.js", "test:create": "node tests/test-mcp-create-records.js", "test:verify": "node tests/final-mcp-verification.js", "test:all": "npm run test:mcp && npm run test:relations && npm run test:create && npm run test:verify", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "bin": {"mcp-server-nocobase": "dist/index.js"}, "keywords": ["mcp", "model-context-protocol", "nocobase", "low-code", "database", "api"], "author": "astra", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.17.1", "axios": "^1.6.0", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "files": ["dist/**/*", "README.md", "LICENSE"], "engines": {"node": ">=18.0.0"}}