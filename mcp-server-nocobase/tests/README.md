# NocoBase MCP Server 测试文件

本目录包含了 NocoBase MCP Server 的各种测试文件，用于验证 MCP 工具的功能和 NocoBase 关联字段的支持。

## 测试文件说明

### 基础功能测试

- **`test-mcp-simple.js`** - MCP 工具基础功能测试
- **`test-mcp-tools.js`** - MCP 工具完整功能测试，包括集合、字段、记录的 CRUD 操作
- **`debug-connection.js`** - 网络连接调试工具，用于解决 SSL 证书问题

### 关联字段测试

- **`test-relation-fields.js`** - 关联字段基础测试
- **`test-relation-fields-complete.js`** - 完整的关联字段测试，包括所有四种关联类型
- **`test-relation-data.js`** - 关联字段数据操作测试

### 完整系统测试

- **`add-teacher-course-relations.js`** - 添加教师-课程关联关系
- **`check-current-relations.js`** - 检查当前关联关系状态
- **`test-mcp-create-records.js`** - 使用 MCP 工具创建记录的完整测试
- **`final-complete-test.js`** - 最终完整的关联关系测试（使用 HTTP 请求）
- **`final-mcp-verification.js`** - 最终完整的关联关系验证（使用 MCP 工具）

### 对比测试

- **`test-api-direct.js`** - 直接 API 调用测试，用于对比 MCP 工具的效果

## 测试的关联关系模型

测试创建了一个完整的学生选课系统，包含以下实体和关联：

```
👨‍🏫 教师 (test_teachers)
   ↓ hasMany (一对多)
📚 课程 (test_courses) ← belongsTo (多对一)
   ↓ hasMany (一对多)        ↘ belongsToMany (多对多)
👨‍🎓 学生 (test_students)    🏷️  标签 (test_tags)
   ↓ hasOne (一对一)
📋 档案 (test_profiles)
```

### 关联类型验证

1. **belongsTo (多对一)**
   - 课程 → 教师 (`test_courses.teacher`)
   - 学生 → 课程 (`test_students.course`)

2. **hasMany (一对多)**
   - 教师 → 课程 (`test_teachers.courses`)
   - 课程 → 学生 (`test_courses.students`)

3. **hasOne (一对一)**
   - 学生 → 档案 (`test_students.profile`)

4. **belongsToMany (多对多)**
   - 课程 → 标签 (`test_courses.tags`)
   - 通过中间表 `test_courses_tags`

## 运行测试

### 环境配置

确保在运行测试前已经：

1. 构建了 MCP 服务器：`npm run build`
2. 配置了正确的 NocoBase 环境
3. 更新了测试文件中的 API 配置

### 推荐的测试顺序

1. **基础连接测试**：
   ```bash
   node tests/debug-connection.js
   ```

2. **MCP 工具功能测试**：
   ```bash
   node tests/test-mcp-tools.js
   ```

3. **关联字段测试**：
   ```bash
   node tests/test-relation-fields-complete.js
   ```

4. **完整数据创建测试**：
   ```bash
   node tests/test-mcp-create-records.js
   ```

5. **最终验证**：
   ```bash
   node tests/final-mcp-verification.js
   ```

## 测试结果

所有测试验证了以下功能：

✅ **MCP 工具完整支持**：
- 集合创建和管理
- 字段创建（包括所有关联字段类型）
- 记录 CRUD 操作
- 关联查询

✅ **NocoBase 关联字段完全支持**：
- 所有四种关联类型都正常工作
- 外键自动管理
- 中间表自动创建
- 关联数据查询正常

✅ **数据完整性**：
- 关联关系正确建立
- 数据一致性保持
- 查询结果准确

## 注意事项

1. **SSL 证书**：开发环境使用自签名证书，需要在客户端配置 `rejectUnauthorized: false`
2. **API 格式**：NocoBase 使用冒号语法的 API 端点，如 `collections:list`
3. **认证头**：需要正确的认证头信息，包括 `X-App`、`X-Role` 等
4. **关联查询**：使用 `appends` 参数获取关联数据

## 贡献

如果需要添加新的测试用例，请遵循现有的命名规范和代码结构。
