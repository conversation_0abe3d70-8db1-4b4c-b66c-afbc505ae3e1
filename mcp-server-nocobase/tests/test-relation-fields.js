#!/usr/bin/env node

/**
 * 测试关联字段功能 - 学生选课系统
 * 测试所有四种关联类型：hasMany, belongsTo, belongsToMany, hasOne
 */

const { spawn } = require('child_process');
const path = require('path');

// 测试配置
const config = {
  serverUrl: 'https://n.astra.xin/api',
  appId: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyNzYwNzgsImV4cCI6MzMzMTE4NzYwNzh9.pcor4Xbillx57TzC44_yhQmCR6AeFCKWYmrXkbO67uM'
};

// 测试步骤
const testSteps = [
  // 第一步：创建基础集合
  {
    name: '创建学生集合',
    tool: 'create_collection',
    params: {
      name: 'test_students',
      title: '测试学生',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },
  {
    name: '创建课程集合',
    tool: 'create_collection',
    params: {
      name: 'test_courses',
      title: '测试课程',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },
  {
    name: '创建教师集合',
    tool: 'create_collection',
    params: {
      name: 'test_teachers',
      title: '测试教师',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },
  {
    name: '创建选课记录集合',
    tool: 'create_collection',
    params: {
      name: 'test_enrollments',
      title: '测试选课记录',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },
  {
    name: '创建学生档案集合',
    tool: 'create_collection',
    params: {
      name: 'test_profiles',
      title: '测试学生档案',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    }
  },

  // 第二步：添加基础字段
  {
    name: '学生-添加姓名字段',
    tool: 'create_field',
    params: {
      collection: 'test_students',
      name: 'name',
      type: 'string',
      interface: 'input',
      description: '学生姓名'
    }
  },
  {
    name: '学生-添加学号字段',
    tool: 'create_field',
    params: {
      collection: 'test_students',
      name: 'student_id',
      type: 'string',
      interface: 'input',
      description: '学号'
    }
  },
  {
    name: '课程-添加课程名字段',
    tool: 'create_field',
    params: {
      collection: 'test_courses',
      name: 'name',
      type: 'string',
      interface: 'input',
      description: '课程名称'
    }
  },
  {
    name: '课程-添加学分字段',
    tool: 'create_field',
    params: {
      collection: 'test_courses',
      name: 'credits',
      type: 'integer',
      interface: 'number',
      description: '学分'
    }
  },
  {
    name: '教师-添加姓名字段',
    tool: 'create_field',
    params: {
      collection: 'test_teachers',
      name: 'name',
      type: 'string',
      interface: 'input',
      description: '教师姓名'
    }
  },
  {
    name: '档案-添加简介字段',
    tool: 'create_field',
    params: {
      collection: 'test_profiles',
      name: 'bio',
      type: 'text',
      interface: 'textarea',
      description: '个人简介'
    }
  },

  // 第三步：添加关联字段
  // belongsTo 关系
  {
    name: '课程-添加教师关联(belongsTo)',
    tool: 'create_field',
    params: {
      collection: 'test_courses',
      name: 'teacher',
      type: 'belongsTo',
      interface: 'm2o',
      description: '授课教师',
      target: 'test_teachers',
      foreignKey: 'teacherId',
      targetKey: 'id'
    }
  },
  {
    name: '选课记录-添加学生关联(belongsTo)',
    tool: 'create_field',
    params: {
      collection: 'test_enrollments',
      name: 'student',
      type: 'belongsTo',
      interface: 'm2o',
      description: '选课学生',
      target: 'test_students',
      foreignKey: 'studentId',
      targetKey: 'id'
    }
  },
  {
    name: '选课记录-添加课程关联(belongsTo)',
    tool: 'create_field',
    params: {
      collection: 'test_enrollments',
      name: 'course',
      type: 'belongsTo',
      interface: 'm2o',
      description: '选修课程',
      target: 'test_courses',
      foreignKey: 'courseId',
      targetKey: 'id'
    }
  },
  {
    name: '档案-添加学生关联(belongsTo)',
    tool: 'create_field',
    params: {
      collection: 'test_profiles',
      name: 'student',
      type: 'belongsTo',
      interface: 'm2o',
      description: '所属学生',
      target: 'test_students',
      foreignKey: 'studentId',
      targetKey: 'id'
    }
  },

  // hasMany 关系
  {
    name: '学生-添加选课记录关联(hasMany)',
    tool: 'create_field',
    params: {
      collection: 'test_students',
      name: 'enrollments',
      type: 'hasMany',
      interface: 'o2m',
      description: '选课记录',
      target: 'test_enrollments',
      foreignKey: 'studentId',
      sourceKey: 'id'
    }
  },
  {
    name: '课程-添加选课记录关联(hasMany)',
    tool: 'create_field',
    params: {
      collection: 'test_courses',
      name: 'enrollments',
      type: 'hasMany',
      interface: 'o2m',
      description: '选课记录',
      target: 'test_enrollments',
      foreignKey: 'courseId',
      sourceKey: 'id'
    }
  },
  {
    name: '教师-添加课程关联(hasMany)',
    tool: 'create_field',
    params: {
      collection: 'test_teachers',
      name: 'courses',
      type: 'hasMany',
      interface: 'o2m',
      description: '授课课程',
      target: 'test_courses',
      foreignKey: 'teacherId',
      sourceKey: 'id'
    }
  },

  // hasOne 关系
  {
    name: '学生-添加档案关联(hasOne)',
    tool: 'create_field',
    params: {
      collection: 'test_students',
      name: 'profile',
      type: 'hasOne',
      interface: 'oho',
      description: '学生档案',
      target: 'test_profiles',
      foreignKey: 'studentId',
      sourceKey: 'id'
    }
  }
];

// 执行测试
async function runTest() {
  console.log('🚀 开始测试关联字段功能...\n');
  
  for (let i = 0; i < testSteps.length; i++) {
    const step = testSteps[i];
    console.log(`📋 步骤 ${i + 1}/${testSteps.length}: ${step.name}`);
    
    try {
      const result = await callMCPTool(step.tool, step.params);
      console.log(`✅ 成功: ${step.name}`);
      if (result.error) {
        console.log(`⚠️  警告: ${result.error}`);
      }
    } catch (error) {
      console.log(`❌ 失败: ${step.name}`);
      console.log(`   错误: ${error.message}`);
      
      // 如果是创建集合失败，可能是已存在，继续执行
      if (step.tool === 'create_collection' && error.message.includes('already exists')) {
        console.log(`   集合已存在，继续执行...`);
        continue;
      }
      
      // 其他错误也继续执行，但记录下来
      console.log(`   继续执行下一步...`);
    }
    
    console.log('');
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('🎉 测试完成！');
  console.log('\n📊 现在可以验证关联关系是否正常工作...');
}

// 调用 MCP 工具
function callMCPTool(tool, params) {
  return new Promise((resolve, reject) => {
    const mcpProcess = spawn('node', [
      path.join(__dirname, 'mcp-server-nocobase/dist/index.js'),
      '--base-url', config.serverUrl,
      '--token', config.token,
      '--app', config.appId
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let error = '';

    mcpProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    mcpProcess.stderr.on('data', (data) => {
      error += data.toString();
    });

    mcpProcess.on('close', (code) => {
      if (code === 0) {
        try {
          const result = JSON.parse(output);
          resolve(result);
        } catch (e) {
          resolve({ success: true, output });
        }
      } else {
        reject(new Error(error || `Process exited with code ${code}`));
      }
    });

    // 发送 MCP 请求
    const request = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/call',
      params: {
        name: tool,
        arguments: params
      }
    };

    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    mcpProcess.stdin.end();
  });
}

// 运行测试
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = { runTest, testSteps };
