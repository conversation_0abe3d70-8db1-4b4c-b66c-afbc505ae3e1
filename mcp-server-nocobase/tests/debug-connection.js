#!/usr/bin/env node

const axios = require('./mcp-server-nocobase/node_modules/axios').default;
const https = require('https');

async function debugConnection() {
  console.log('🔍 调试网络连接...\n');

  const config = {
    baseURL: 'https://app.dev.orb.local/api',
    headers: {
      "Authorization": `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA`,
      "X-App": "mcp_playground",
      "Content-Type": "application/json",
      "Accept": "*/*",
      "X-Locale": "en-US",
      "X-Role": "admin",
      "X-Authenticator": "basic",
      "X-Timezone": "+08:00",
      "X-Hostname": "app.dev.orb.local"
    },
    timeout: 30000,
    httpsAgent: new https.Agent({
      rejectUnauthorized: false // 忽略自签名证书
    })
  };

  try {
    console.log('📡 测试基本连接...');
    const client = axios.create(config);
    
    console.log('🔗 请求URL:', config.baseURL + '/collections:list');
    console.log('📋 请求头:', JSON.stringify(config.headers, null, 2));
    
    const response = await client.get('/collections:list');
    console.log('✅ 连接成功!');
    console.log('📊 响应状态:', response.status);
    console.log('📄 响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ 连接失败:');
    console.log('错误类型:', error.constructor.name);
    console.log('错误消息:', error.message);
    
    if (error.response) {
      console.log('响应状态:', error.response.status);
      console.log('响应头:', JSON.stringify(error.response.headers, null, 2));
      console.log('响应数据:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('请求对象:', error.request.constructor.name);
      console.log('请求配置:', JSON.stringify({
        method: error.config?.method,
        url: error.config?.url,
        baseURL: error.config?.baseURL,
        timeout: error.config?.timeout
      }, null, 2));
    } else {
      console.log('其他错误:', error.message);
    }
    
    // 尝试更详细的错误信息
    if (error.code) {
      console.log('错误代码:', error.code);
    }
    if (error.errno) {
      console.log('错误号:', error.errno);
    }
    if (error.syscall) {
      console.log('系统调用:', error.syscall);
    }
    if (error.hostname) {
      console.log('主机名:', error.hostname);
    }
  }
}

debugConnection().catch(console.error);
