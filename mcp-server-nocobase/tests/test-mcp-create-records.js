#!/usr/bin/env node

// 使用MCP工具创建记录的测试
const { NocoBaseClient } = require('./mcp-server-nocobase/dist/client.js');

async function testMCPCreateRecords() {
  console.log('🚀 使用MCP工具创建记录测试...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 1. 创建更多教师记录
    console.log('📋 步骤1: 创建更多教师记录');
    const newTeachers = [
      { name: '王教授' },
      { name: '赵老师' },
      { name: '陈教授' }
    ];

    for (const teacherData of newTeachers) {
      try {
        const teacher = await client.createRecord('test_teachers', teacherData);
        console.log(`✅ 成功创建教师: ${teacher.name} (ID: ${teacher.id})`);
      } catch (error) {
        console.log(`❌ 创建教师 ${teacherData.name} 失败: ${error.message}`);
      }
    }
    console.log();

    // 2. 创建更多课程记录
    console.log('📋 步骤2: 创建更多课程记录');
    const newCourses = [
      { name: '概率论与数理统计' },
      { name: '离散数学' },
      { name: '数据结构' }
    ];

    for (const courseData of newCourses) {
      try {
        const course = await client.createRecord('test_courses', courseData);
        console.log(`✅ 成功创建课程: ${course.name} (ID: ${course.id})`);
      } catch (error) {
        console.log(`❌ 创建课程 ${courseData.name} 失败: ${error.message}`);
      }
    }
    console.log();

    // 3. 创建更多学生记录
    console.log('📋 步骤3: 创建更多学生记录');
    const newStudents = [
      { name: '张三' },
      { name: '李四' },
      { name: '王五' },
      { name: '赵六' }
    ];

    for (const studentData of newStudents) {
      try {
        const student = await client.createRecord('test_students', studentData);
        console.log(`✅ 成功创建学生: ${student.name} (ID: ${student.id})`);
      } catch (error) {
        console.log(`❌ 创建学生 ${studentData.name} 失败: ${error.message}`);
      }
    }
    console.log();

    // 4. 获取所有数据并建立关联
    console.log('📋 步骤4: 获取数据并建立关联关系');
    
    const teachers = await client.listRecords('test_teachers');
    const courses = await client.listRecords('test_courses');
    const students = await client.listRecords('test_students');
    
    console.log(`✅ 当前有 ${teachers.data.length} 个教师`);
    console.log(`✅ 当前有 ${courses.data.length} 个课程`);
    console.log(`✅ 当前有 ${students.data.length} 个学生`);
    console.log();

    // 5. 为新课程分配教师
    console.log('📋 步骤5: 为新课程分配教师');
    
    // 获取新创建的课程（ID > 2的课程）
    const newCoursesData = courses.data.filter(c => c.id > 2);
    const availableTeachers = teachers.data.filter(t => t.id > 3); // 新教师
    
    for (let i = 0; i < newCoursesData.length && i < availableTeachers.length; i++) {
      const course = newCoursesData[i];
      const teacher = availableTeachers[i];
      
      try {
        await client.updateRecord('test_courses', course.id, {
          teacherId: teacher.id
        });
        console.log(`✅ 成功分配: ${teacher.name} 教授 ${course.name}`);
      } catch (error) {
        console.log(`❌ 分配教师失败: ${error.message}`);
      }
    }
    console.log();

    // 6. 为学生分配课程
    console.log('📋 步骤6: 为学生分配课程');
    
    const newStudentsData = students.data.filter(s => s.id > 2); // 新学生
    const availableCourses = courses.data;
    
    for (let i = 0; i < newStudentsData.length && i < availableCourses.length; i++) {
      const student = newStudentsData[i];
      const course = availableCourses[i % availableCourses.length]; // 循环分配课程
      
      try {
        await client.updateRecord('test_students', student.id, {
          courseId: course.id
        });
        console.log(`✅ 成功分配: ${student.name} 选修 ${course.name}`);
      } catch (error) {
        console.log(`❌ 分配课程失败: ${error.message}`);
      }
    }
    console.log();

    // 7. 为学生创建档案
    console.log('📋 步骤7: 为学生创建档案');
    
    for (const student of newStudentsData) {
      try {
        const profile = await client.createRecord('test_profiles', {
          studentId: student.id,
          bio: `这是${student.name}的个人档案，学号：${student.id.toString().padStart(6, '0')}`
        });
        console.log(`✅ 成功创建档案: ${student.name} (档案ID: ${profile.id})`);
      } catch (error) {
        console.log(`❌ 创建档案失败: ${error.message}`);
      }
    }
    console.log();

    // 8. 创建更多标签
    console.log('📋 步骤8: 创建更多标签');
    
    const newTags = [
      { name: '理工科' },
      { name: '计算机' },
      { name: '选修课' },
      { name: '核心课程' }
    ];

    const createdTags = [];
    for (const tagData of newTags) {
      try {
        const tag = await client.createRecord('test_tags', tagData);
        createdTags.push(tag);
        console.log(`✅ 成功创建标签: ${tag.name} (ID: ${tag.id})`);
      } catch (error) {
        console.log(`❌ 创建标签 ${tagData.name} 失败: ${error.message}`);
      }
    }
    console.log();

    // 9. 为课程添加标签（通过中间表）
    console.log('📋 步骤9: 为课程添加标签');
    
    const allTags = await client.listRecords('test_tags');
    
    for (let i = 0; i < courses.data.length; i++) {
      const course = courses.data[i];
      const tag = allTags.data[i % allTags.data.length]; // 循环分配标签
      
      try {
        await client.createRecord('test_courses_tags', {
          courseId: course.id,
          tagId: tag.id
        });
        console.log(`✅ 成功关联: ${course.name} ← → ${tag.name}`);
      } catch (error) {
        if (!error.message.includes('duplicate') && !error.message.includes('already exists')) {
          console.log(`❌ 创建课程标签关联失败: ${error.message}`);
        } else {
          console.log(`ℹ️  关联已存在: ${course.name} ← → ${tag.name}`);
        }
      }
    }
    console.log();

    // 10. 最终数据统计
    console.log('📊 === 最终数据统计 ===');
    
    const finalTeachers = await client.listRecords('test_teachers');
    const finalCourses = await client.listRecords('test_courses');
    const finalStudents = await client.listRecords('test_students');
    const finalProfiles = await client.listRecords('test_profiles');
    const finalTags = await client.listRecords('test_tags');
    const finalRelations = await client.listRecords('test_courses_tags');
    
    console.log(`👨‍🏫 教师总数: ${finalTeachers.data.length}`);
    console.log(`📚 课程总数: ${finalCourses.data.length}`);
    console.log(`👨‍🎓 学生总数: ${finalStudents.data.length}`);
    console.log(`📋 档案总数: ${finalProfiles.data.length}`);
    console.log(`🏷️  标签总数: ${finalTags.data.length}`);
    console.log(`🔗 课程标签关联总数: ${finalRelations.data.length}`);

    console.log('\n🎉 使用MCP工具创建记录测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

testMCPCreateRecords().catch(console.error);
