#!/usr/bin/env node

/**
 * 直接测试 NocoBase API 连接和集合创建
 */

const baseURL = 'https://n.astra.xin/api';
const headers = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyNzYwNzgsImV4cCI6MzMzMTE4NzYwNzh9.pcor4Xbillx57TzC44_yhQmCR6AeFCKWYmrXkbO67uM',
  'X-App': 'mcp_playground',
  'Content-Type': 'application/json'
};

async function apiRequest(method, path, data = null) {
  const url = baseURL + path;
  const options = {
    method,
    headers,
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);
  const result = await response.json();

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${JSON.stringify(result)}`);
  }

  return result;
}

async function testAPI() {
  console.log('🔍 测试 NocoBase API 连接...\n');

  try {
    // 1. 测试基本连接 - 获取现有集合
    console.log('📋 1. 获取现有集合列表...');
    const collectionsResponse = await apiRequest('GET', '/collections');
    console.log(`✅ 成功获取 ${collectionsResponse.data.length} 个集合`);

    const existingCollections = collectionsResponse.data.map(c => c.name);
    console.log('现有集合:', existingCollections.join(', '));
    console.log('');

    // 2. 检查测试集合是否已存在
    const testCollections = ['test_students', 'test_courses', 'test_teachers', 'test_enrollments', 'test_profiles'];
    const existingTestCollections = testCollections.filter(name => existingCollections.includes(name));
    
    if (existingTestCollections.length > 0) {
      console.log('⚠️  发现已存在的测试集合:', existingTestCollections.join(', '));
      console.log('');
    }

    // 3. 创建一个简单的测试集合
    const testCollectionName = 'test_simple_' + Date.now();
    console.log(`📝 2. 创建测试集合: ${testCollectionName}...`);

    const createResponse = await apiRequest('POST', '/collections:create', {
      name: testCollectionName,
      title: '简单测试集合',
      autoGenId: true,
      createdAt: true,
      updatedAt: true
    });

    console.log('✅ 集合创建成功!');
    console.log('集合信息:', {
      name: createResponse.data.name,
      title: createResponse.data.title,
      id: createResponse.data.id
    });
    console.log('');

    // 4. 为测试集合添加一个简单字段
    console.log(`📝 3. 为集合添加字段...`);

    const fieldResponse = await apiRequest('POST', `/collections/${testCollectionName}/fields:create`, {
      name: 'test_name',
      type: 'string',
      interface: 'input',
      uiSchema: {
        type: 'string',
        title: '测试名称',
        'x-component': 'Input'
      }
    });

    console.log('✅ 字段创建成功!');
    console.log('字段信息:', {
      name: fieldResponse.data.name,
      type: fieldResponse.data.type,
      interface: fieldResponse.data.interface
    });
    console.log('');

    // 5. 验证集合和字段
    console.log(`🔍 4. 验证集合和字段...`);

    const verifyResponse = await apiRequest('GET', `/collections/${testCollectionName}`);
    const fields = verifyResponse.data.fields || [];

    console.log('✅ 验证成功!');
    console.log(`集合 ${testCollectionName} 包含 ${fields.length} 个字段:`);
    fields.forEach(field => {
      console.log(`  - ${field.name} (${field.type})`);
    });
    console.log('');

    // 6. 测试关联字段创建
    console.log(`📝 5. 测试创建关联字段...`);

    // 先创建目标集合
    const targetCollectionName = 'test_target_' + Date.now();
    await apiRequest('POST', '/collections:create', {
      name: targetCollectionName,
      title: '目标集合',
      autoGenId: true
    });

    // 创建 belongsTo 关联字段
    const relationResponse = await apiRequest('POST', `/collections/${testCollectionName}/fields:create`, {
      name: 'target_relation',
      type: 'belongsTo',
      interface: 'm2o',
      target: targetCollectionName,
      foreignKey: 'targetId',
      targetKey: 'id',
      uiSchema: {
        title: '关联目标',
        'x-component': 'AssociationField'
      }
    });

    console.log('✅ 关联字段创建成功!');
    console.log('关联字段信息:', {
      name: relationResponse.data.name,
      type: relationResponse.data.type,
      target: relationResponse.data.target
    });
    console.log('');

    console.log('🎉 所有测试通过! NocoBase API 工作正常。');
    
    // 清理测试数据
    console.log('\n🧹 清理测试数据...');
    try {
      await apiRequest('DELETE', `/collections/${testCollectionName}:destroy`);
      await apiRequest('DELETE', `/collections/${targetCollectionName}:destroy`);
      console.log('✅ 测试数据清理完成');
    } catch (cleanupError) {
      console.log('⚠️  清理测试数据时出现错误:', cleanupError.message);
    }

  } catch (error) {
    console.error('❌ API 测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.error('网络错误:', error.message);
    } else {
      console.error('错误:', error.message);
    }
  }
}

// 运行测试
if (require.main === module) {
  testAPI();
}

module.exports = { testAPI };
