# NocoBase MCP Server

A Model Context Protocol (MCP) server for NocoBase, providing seamless integration between AI assistants and NocoBase low-code platform.

## Features

- **Complete CRUD Operations**: Create, read, update, and delete collections, fields, and records
- **Full Association Support**: All four NocoBase association types (belongsTo, hasMany, hasOne, belongsToMany)
- **Schema Management**: Dynamic field creation and collection management
- **Type Safety**: Full TypeScript support with proper type definitions
- **Error Handling**: Comprehensive error handling and validation

## Supported Association Types

✅ **belongsTo (Many-to-One)**
- Example: Student → Course, Course → Teacher

✅ **hasMany (One-to-Many)**  
- Example: Teacher → Courses, Course → Students

✅ **hasOne (One-to-One)**
- Example: Student → Profile

✅ **belongsToMany (Many-to-Many)**
- Example: Course → Tags (through junction table)

## Installation

```bash
npm install
npm run build
```

## Configuration

Configure your NocoBase connection in the MCP client:

```json
{
  "baseUrl": "https://your-nocobase-instance.com/api",
  "token": "your-auth-token",
  "app": "your-app-id"
}
```

## Available Tools

### Collection Management
- `list_collections` - List all collections
- `get_collection` - Get collection details
- `create_collection` - Create new collection
- `update_collection` - Update collection
- `delete_collection` - Delete collection

### Field Management
- `get_collection_schema` - Get collection schema
- `list_fields` - List collection fields
- `create_field` - Create new field (including association fields)

### Record Management
- `list_records` - List records with pagination
- `get_record` - Get single record with associations
- `create_record` - Create new record
- `update_record` - Update existing record
- `delete_record` - Delete record

## Usage Examples

### Creating Collections and Fields

```javascript
// Create a collection
await client.createCollection({
  name: 'students',
  title: 'Students',
  autoGenId: true,
  createdAt: true,
  updatedAt: true
});

// Create a text field
await client.createField('students', {
  name: 'name',
  type: 'string',
  interface: 'input',
  uiSchema: {
    title: 'Student Name',
    'x-component': 'Input'
  }
});

// Create an association field (belongsTo)
await client.createField('students', {
  name: 'course',
  type: 'belongsTo',
  interface: 'm2o',
  target: 'courses',
  foreignKey: 'courseId',
  targetKey: 'id',
  uiSchema: {
    title: 'Course',
    'x-component': 'AssociationField'
  }
});
```

### Working with Records

```javascript
// Create a record
const student = await client.createRecord('students', {
  name: 'John Doe',
  courseId: 1
});

// Get record with associations
const studentWithCourse = await client.getRecord('students', student.id, ['course']);

// Update record
await client.updateRecord('students', student.id, {
  name: 'John Smith'
});

// List records with pagination
const students = await client.listRecords('students', {
  page: 1,
  pageSize: 10,
  appends: ['course', 'profile']
});
```

## Testing

The project includes comprehensive tests in the `tests/` directory:

```bash
# Run basic MCP tools test
node tests/test-mcp-tools.js

# Run association fields test
node tests/test-relation-fields-complete.js

# Run complete system test
node tests/final-mcp-verification.js
```

See [tests/README.md](tests/README.md) for detailed testing documentation.

## Development

### Building

```bash
npm run build
```

### Running in Development

```bash
npm run dev
```

### Project Structure

```
src/
├── index.ts          # MCP server entry point
├── client.ts         # NocoBase API client
├── tools/
│   ├── collections.ts # Collection management tools
│   ├── schema.ts     # Field/schema management tools
│   └── records.ts    # Record management tools
└── types/            # TypeScript type definitions

tests/                # Comprehensive test suite
├── README.md         # Testing documentation
├── test-mcp-tools.js # Basic functionality tests
├── test-relation-*.js # Association field tests
└── final-*.js        # Complete system tests
```

## SSL Certificate Handling

For development environments with self-signed certificates, the client automatically handles SSL certificate validation:

```typescript
httpsAgent: new https.Agent({
  rejectUnauthorized: false // For development only
})
```

## API Compatibility

This MCP server is compatible with NocoBase's REST API format:

- Endpoint format: `/collections:list`, `/collections/name/fields:create`
- Authentication: Bearer token with additional headers
- Response format: Standard NocoBase API responses

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Support

For issues and questions:
- Check the [tests/](tests/) directory for usage examples
- Review the comprehensive test suite for implementation patterns
- Open an issue on GitHub for bugs or feature requests
