import axios, { type AxiosInstance, type AxiosResponse } from "axios";
import https from "https";

export interface NocoBaseConfig {
  baseUrl: string;
  token: string;
  app: string;
}

export interface Collection {
  key: string;
  name: string;
  title: string;
  inherit: boolean;
  hidden: boolean;
  description?: string;
  autoGenId: boolean;
  createdAt: boolean;
  updatedAt: boolean;
  createdBy: boolean;
  updatedBy: boolean;
  filterTargetKey: string;
  unavailableActions: string[];
  fields?: Field[];
}

export interface Field {
  key: string;
  name: string;
  type: string;
  interface: string;
  collectionName: string;
  description?: string;
  uiSchema?: any;
  [key: string]: any;
}

export interface Record {
  id: number | string;
  [key: string]: any;
}

export interface DesktopRoute {
  id?: number | string;
  parentId?: number | string;
  type: 'page' | 'tab' | 'group' | 'link';
  title: string;
  icon?: string;
  tooltip?: string;
  schemaUid?: string;
  menuSchemaUid?: string;
  tabSchemaName?: string;
  pageSchemaUid?: string;
  sort?: number;
  options?: {
    href?: string;
    params?: Array<{ name: string; value: any }>;
    openInNewWindow?: boolean;
    [key: string]: any;
  };
  enableTabs?: boolean;
  enableHeader?: boolean;
  displayTitle?: boolean;
  hidden?: boolean;
  hideInMenu?: boolean;
  children?: DesktopRoute[];
  roles?: Array<{ name: string; title: string }>;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: any;
  updatedBy?: any;
  [key: string]: any;
}

export interface ApiResponse<T = any> {
  data: T;
  meta?: {
    count: number;
    page: number;
    pageSize: number;
    totalPage: number;
  };
}

export class NocoBaseClient {
  private client: AxiosInstance;
  private config: NocoBaseConfig;

  constructor(config: NocoBaseConfig) {
    this.config = config;
    this.client = axios.create({
      baseURL: config.baseUrl,
      headers: {
        "Authorization": `Bearer ${config.token}`,
        "X-App": config.app,
        "Content-Type": "application/json",
        "Accept": "*/*",
        "X-Locale": "en-US",
        "X-Role": "admin",
        "X-Authenticator": "basic",
        "X-Timezone": "+08:00",
        "X-Hostname": "app.dev.orb.local"
      },
      timeout: 30000,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false // 忽略自签名证书
      })
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          const message = error.response.data?.errors?.[0]?.message || error.response.statusText;
          throw new Error(`NocoBase API Error (${error.response.status}): ${message}`);
        } else if (error.request) {
          throw new Error("NocoBase API Error: No response received");
        } else {
          throw new Error(`NocoBase API Error: ${error.message}`);
        }
      }
    );
  }

  // Collections API
  async listCollections(): Promise<Collection[]> {
    const response: AxiosResponse<ApiResponse<Collection[]>> = await this.client.get("/collections:list");
    return response.data.data;
  }

  async listCollectionsMeta(): Promise<Collection[]> {
    const response: AxiosResponse<ApiResponse<Collection[]>> = await this.client.get("/collections:listMeta");
    return response.data.data;
  }

  async getCollection(name: string): Promise<Collection> {
    const response: AxiosResponse<ApiResponse<Collection>> = await this.client.get(
      `/collections:get?filterByTk=${name}&appends[]=fields`
    );
    return response.data.data;
  }

  async createCollection(collection: Partial<Collection>): Promise<Collection> {
    const response: AxiosResponse<ApiResponse<Collection>> = await this.client.post(
      "/collections:create",
      collection
    );
    return response.data.data;
  }

  async updateCollection(name: string, updates: Partial<Collection>): Promise<Collection> {
    const response: AxiosResponse<ApiResponse<Collection>> = await this.client.post(
      `/collections:update?filterByTk=${name}`,
      updates
    );
    return response.data.data;
  }

  async deleteCollection(name: string): Promise<void> {
    await this.client.post(`/collections:destroy?filterByTk=${name}`);
  }

  // Fields API
  async listFields(collectionName: string): Promise<Field[]> {
    const response: AxiosResponse<ApiResponse<Field[]>> = await this.client.get(
      `/collections/${collectionName}/fields:list`
    );
    return response.data.data;
  }

  async getField(collectionName: string, fieldName: string): Promise<Field> {
    const response: AxiosResponse<ApiResponse<Field>> = await this.client.get(
      `/collections/${collectionName}/fields:get?filterByTk=${fieldName}`
    );
    return response.data.data;
  }

  async createField(collectionName: string, field: Partial<Field>): Promise<Field> {
    const response: AxiosResponse<ApiResponse<Field>> = await this.client.post(
      `/collections/${collectionName}/fields:create`,
      field
    );
    return response.data.data;
  }

  async updateField(collectionName: string, fieldName: string, updates: Partial<Field>): Promise<Field> {
    const response: AxiosResponse<ApiResponse<Field>> = await this.client.post(
      `/collections/${collectionName}/fields:update?filterByTk=${fieldName}`,
      updates
    );
    return response.data.data;
  }

  // Records API
  async listRecords(collectionName: string, options?: {
    page?: number;
    pageSize?: number;
    filter?: any;
    sort?: string[];
    appends?: string[];
  }): Promise<{ data: Record[]; meta?: any }> {
    const params = new URLSearchParams();
    
    if (options?.page) params.append("page", options.page.toString());
    if (options?.pageSize) params.append("pageSize", options.pageSize.toString());
    if (options?.filter) params.append("filter", JSON.stringify(options.filter));
    if (options?.sort) {
      options.sort.forEach(s => params.append("sort[]", s));
    }
    if (options?.appends) {
      options.appends.forEach(a => params.append("appends[]", a));
    }

    const response: AxiosResponse<ApiResponse<Record[]>> = await this.client.get(
      `/${collectionName}:list?${params.toString()}`
    );
    return { data: response.data.data, meta: response.data.meta };
  }

  async getRecord(collectionName: string, id: string | number, appends?: string[]): Promise<Record> {
    const params = new URLSearchParams();
    params.append("filterByTk", id.toString());

    if (appends) {
      appends.forEach(a => params.append("appends[]", a));
    }

    const response: AxiosResponse<ApiResponse<Record>> = await this.client.get(
      `/${collectionName}:get?${params.toString()}`
    );
    return response.data.data;
  }

  async createRecord(collectionName: string, data: Partial<Record>): Promise<Record> {
    const response: AxiosResponse<ApiResponse<Record>> = await this.client.post(
      `/${collectionName}:create`,
      data
    );
    return response.data.data;
  }

  async updateRecord(collectionName: string, id: string | number, data: Partial<Record>): Promise<Record> {
    const response: AxiosResponse<ApiResponse<Record>> = await this.client.post(
      `/${collectionName}:update?filterByTk=${id}`,
      data
    );
    return response.data.data;
  }

  async deleteRecord(collectionName: string, id: string | number): Promise<void> {
    await this.client.post(`/${collectionName}:destroy?filterByTk=${id}`);
  }

  // Routes API
  async listRoutes(options?: { tree?: boolean }): Promise<DesktopRoute[]> {
    const params = options?.tree ? "?tree=true" : "";
    const response: AxiosResponse<ApiResponse<DesktopRoute[]>> = await this.client.get(
      `/desktopRoutes:listAccessible${params}`
    );
    return response.data.data;
  }

  async getRoute(id: string | number): Promise<DesktopRoute> {
    const response: AxiosResponse<ApiResponse<DesktopRoute>> = await this.client.get(
      `/desktopRoutes:get?filterByTk=${id}`
    );
    return response.data.data;
  }

  async createRoute(route: Partial<DesktopRoute>): Promise<DesktopRoute> {
    const response: AxiosResponse<ApiResponse<DesktopRoute>> = await this.client.post(
      "/desktopRoutes:create",
      route
    );
    return response.data.data;
  }

  async updateRoute(id: string | number, updates: Partial<DesktopRoute>): Promise<DesktopRoute> {
    const response: AxiosResponse<ApiResponse<DesktopRoute>> = await this.client.post(
      `/desktopRoutes:update?filterByTk=${id}`,
      updates
    );
    return response.data.data;
  }

  async deleteRoute(id: string | number): Promise<void> {
    await this.client.post(`/desktopRoutes:destroy?filterByTk=${id}`);
  }

  async moveRoute(options: {
    sourceId: string | number;
    targetId?: string | number;
    targetScope?: any;
    sortField?: string;
    sticky?: boolean;
    method?: 'insertAfter' | 'prepend';
  }): Promise<void> {
    await this.client.post("/desktopRoutes:move", options);
  }

  // UI Schema API
  async createPageSchema(schemaUid: string, schema: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      "/uiSchemas:insert",
      {
        'x-uid': schemaUid,
        schema: schema
      }
    );
    return response.data.data;
  }

  async getPageSchema(schemaUid: string): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.get(
      `/uiSchemas:getJsonSchema/${schemaUid}`
    );
    return response.data.data;
  }

  async insertBlockSchema(parentUid: string, blockSchema: any, position?: string): Promise<any> {
    // 对于页面级别的插入，我们需要插入到页面的 grid 属性中
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      "/uiSchemas:insertAdjacent",
      {
        parentUid,
        schema: blockSchema,
        position: position || 'beforeEnd',
        wrap: null // 确保不包装
      }
    );
    return response.data.data;
  }

  async insertBlockToGrid(pageUid: string, blockSchema: any, position?: string): Promise<any> {
    // 专门用于向页面的 Grid 中插入区块的方法
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      "/uiSchemas:insertAdjacent",
      {
        parentUid: `${pageUid}.grid`, // 使用 grid 属性路径
        schema: blockSchema,
        position: position || 'beforeEnd'
      }
    );
    return response.data.data;
  }

  async updateBlockSchema(blockUid: string, updates: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/uiSchemas:patch`,
      {
        'x-uid': blockUid,
        ...updates
      }
    );
    return response.data.data;
  }

  async deleteBlockSchema(blockUid: string): Promise<void> {
    await this.client.post(`/uiSchemas:remove`, {
      'x-uid': blockUid
    });
  }

  async getSchemaProperties(schemaUid: string): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.get(
      `/uiSchemas:getProperties/${schemaUid}`
    );
    return response.data.data;
  }
}
