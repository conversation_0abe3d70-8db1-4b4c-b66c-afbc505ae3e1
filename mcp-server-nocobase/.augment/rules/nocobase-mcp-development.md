---
type: "always_apply"
---

The following NocoBase development environment configuration for testing the MCP server:

**Environment Details:**
- **Admin Dashboard URL**: "https://app.dev.orb.local/apps/mcp_playground/admin"
- **Test Credentials**: 
  - Username: `neo`
  - Password: `neo@123`
- **API Configuration**:
  - Base URL: `https://app.dev.orb.local/api`
  - App ID: `mcp_playground`
  - Authorization Token: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA`

**Testing Methods:**
1. **UI Testing**: Use Playwright to automate interactions with the admin dashboard at the provided URL
2. **API Testing**: Make direct HTTP requests to the API endpoints using the provided base URL, app ID, and authorization token

**Usage Context**: This environment should be used for testing the mcp-server-nocobase integration, validating that the MCP tools can properly interact with NocoBase collections, records, and schemas through both the web interface and REST API.

# 使用 deep_graph 查阅代码
使用 deep_graph MCP 查阅代码相关信息。